class XAUUSDCalculator {
    constructor() {
        this.form = document.getElementById('positionForm');
        this.resultSection = document.getElementById('result');
        this.initEventListeners();
    }

    initEventListeners() {
        this.form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.calculatePosition();
        });

        // 实时验证输入
        const inputs = this.form.querySelectorAll('input, select');
        inputs.forEach(input => {
            input.addEventListener('input', () => {
                this.validateInput(input);
            });
        });
    }

    validateInput(input) {
        const value = parseFloat(input.value);
        
        // 移除之前的样式
        input.classList.remove('error', 'success');
        
        if (input.value === '') return;

        switch (input.id) {
            case 'riskAmount':
                if (value <= 0) {
                    input.classList.add('error');
                } else {
                    input.classList.add('success');
                }
                break;
            case 'entryPrice':
            case 'stopLoss':
                if (value <= 0) {
                    input.classList.add('error');
                } else {
                    input.classList.add('success');
                }
                break;
        }
    }

    calculatePosition() {
        // 获取输入值
        const riskAmount = parseFloat(document.getElementById('riskAmount').value);
        const entryPrice = parseFloat(document.getElementById('entryPrice').value);
        const stopLoss = parseFloat(document.getElementById('stopLoss').value);
        const direction = document.getElementById('direction').value;

        // 验证输入
        if (!this.validateInputs(riskAmount, entryPrice, stopLoss, direction)) {
            return;
        }

        // 计算风险距离
        const riskDistance = Math.abs(entryPrice - stopLoss);
        
        // 验证交易方向逻辑
        if (!this.validateTradeDirection(entryPrice, stopLoss, direction)) {
            return;
        }

        // 计算仓位大小
        // XAUUSD: 0.01手 = 1美金/点波动
        const positionSize = riskAmount / riskDistance;

        // 显示结果
        this.displayResults({
            positionSize: positionSize,
            riskDistance: riskDistance,
            maxLoss: riskAmount,
            entryPrice: entryPrice,
            stopLoss: stopLoss,
            direction: direction
        });
    }

    validateInputs(riskAmount, entryPrice, stopLoss, direction) {
        let isValid = true;
        let errorMessage = '';

        if (isNaN(riskAmount) || riskAmount <= 0) {
            errorMessage = '请输入有效的风险金额';
            isValid = false;
        } else if (isNaN(entryPrice) || entryPrice <= 0) {
            errorMessage = '请输入有效的入场价格';
            isValid = false;
        } else if (isNaN(stopLoss) || stopLoss <= 0) {
            errorMessage = '请输入有效的止损价格';
            isValid = false;
        } else if (!direction) {
            errorMessage = '请选择交易方向';
            isValid = false;
        } else if (entryPrice === stopLoss) {
            errorMessage = '入场价格不能等于止损价格';
            isValid = false;
        }

        if (!isValid) {
            this.showWarning(errorMessage);
        }

        return isValid;
    }

    validateTradeDirection(entryPrice, stopLoss, direction) {
        let isValid = true;
        let warningMessage = '';

        if (direction === 'long' && stopLoss >= entryPrice) {
            warningMessage = '做多交易的止损价格应该低于入场价格';
            isValid = false;
        } else if (direction === 'short' && stopLoss <= entryPrice) {
            warningMessage = '做空交易的止损价格应该高于入场价格';
            isValid = false;
        }

        if (!isValid) {
            this.showWarning(warningMessage);
        }

        return isValid;
    }

    displayResults(data) {
        const { positionSize, riskDistance, maxLoss, entryPrice, stopLoss, direction } = data;

        // 更新结果显示
        document.getElementById('positionSize').textContent = `${positionSize.toFixed(2)} 手`;
        document.getElementById('riskDistance').textContent = `$${riskDistance.toFixed(2)}`;
        document.getElementById('maxLoss').textContent = `$${maxLoss.toFixed(2)}`;

        // 计算风险回报比（假设目标利润等于风险距离）
        const riskRewardRatio = `1:1`;
        document.getElementById('riskReward').textContent = riskRewardRatio;

        // 显示结果区域
        this.resultSection.style.display = 'block';

        // 检查是否需要显示警告
        this.checkPositionWarnings(positionSize, riskDistance);

        // 滚动到结果区域
        this.resultSection.scrollIntoView({ behavior: 'smooth' });
    }

    checkPositionWarnings(positionSize, riskDistance) {
        const warningElement = document.getElementById('warningMessage');
        const warningText = document.getElementById('warningText');
        
        let warnings = [];

        // 检查仓位大小
        if (positionSize > 10) {
            warnings.push('仓位较大，请确认风险承受能力');
        }

        // 检查风险距离
        if (riskDistance < 1) {
            warnings.push('止损距离较小，可能容易被市场噪音触发');
        } else if (riskDistance > 50) {
            warnings.push('止损距离较大，请确认是否合理');
        }

        // 显示警告
        if (warnings.length > 0) {
            warningText.textContent = warnings.join('；');
            warningElement.style.display = 'block';
        } else {
            warningElement.style.display = 'none';
        }
    }

    showWarning(message) {
        const warningElement = document.getElementById('warningMessage');
        const warningText = document.getElementById('warningText');
        
        warningText.textContent = message;
        warningElement.style.display = 'block';
        
        // 隐藏结果区域
        this.resultSection.style.display = 'none';
    }
}

// 页面加载完成后初始化计算器
document.addEventListener('DOMContentLoaded', () => {
    new XAUUSDCalculator();
});

// 添加一些实用功能
document.addEventListener('DOMContentLoaded', () => {
    // 添加快捷键支持
    document.addEventListener('keydown', (e) => {
        if (e.ctrlKey && e.key === 'Enter') {
            document.getElementById('positionForm').dispatchEvent(new Event('submit'));
        }
    });

    // 添加数字输入格式化
    const numberInputs = document.querySelectorAll('input[type="number"]');
    numberInputs.forEach(input => {
        input.addEventListener('blur', () => {
            if (input.value) {
                const value = parseFloat(input.value);
                if (!isNaN(value)) {
                    input.value = value.toFixed(2);
                }
            }
        });
    });
});
